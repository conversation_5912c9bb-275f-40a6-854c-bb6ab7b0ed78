plugins {
  id "ns-patch" version "${patchPluginVersion}"
}

apply plugin: 'netservice'

patch {
    includeVersionFile false
    basename = 'SIC_Penale'
}

releaseNotes{
    withAs true
	withDb false

    modalitaApplicazioneDB "senza_fermo"
    //modalitaApplicazioneDB "con_fermo"

    //interventiEseguiti {
    //    descrizione = "Risoluzione ticket w-tresys MEV 34637"
    //}

    modificheEvolutive{
       descrizione = "Contratto CIG B2BD866C4A PLO25 MIG Avvisi d'udienza SF-SU"
    }

    /*dipendenzeSistemi{
        descrizione = "Installare prima la patch CASSAZIONE_PENALE_DB_1.03.00"
    }
    applicazionePatchDB {
        descrizione = "Applicare prima la patch CASSAZIONE_PENALE_DB_1.03.00"
    }
    */
    applicazionePatchAS {
    descrizione = "Per eseguire l'aggiornamento degli application server eseguire la seguente procedura per ogni macchina AS:"
    elenco = [
            "Spegnere il servizio di JBoss",
            "Eliminare l'ear contenuto nella cartella deployments di JBoss",
   			"Copiare l'ear contenuto nella presente patch all'interno della cartella deployments",
 //           "Eseguire sullo schema MIGRA del db del SIC PENALE lo script riportato nel paragrafo MODIFICHE DB",
//            "Applicare le eventuali configurazioni contenute nel paragrafo relativo",
            "Avviare il servizio di JBoss"
    ]

    }


//    modificheConfigurazione {
//        descrizione = "Eliminare nel file sic-penale.properties le seguenti properties:"
//        elenco = [         "notifiche.abilitaCartabia=true\n",
//                            "notifiche.abilitaTransitorioCartabia=false\n"]
//    }


}

dependencies {
    // these go to ear subdir
    deploy project(path: ':ear-sic-penale', configuration:'eardep')
}
