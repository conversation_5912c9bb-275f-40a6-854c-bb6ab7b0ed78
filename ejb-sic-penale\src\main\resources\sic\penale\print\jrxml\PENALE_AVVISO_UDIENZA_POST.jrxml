<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version last-->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="PENALE_AVVISO_UDIENZA" pageWidth="595" pageHeight="842" columnWidth="555" leftMargin="20" rightMargin="20" topMargin="20" bottomMargin="20" uuid="d415275c-cd07-490c-9906-d287f6a8fd18">
	<property name="com.jaspersoft.studio.data.defaultdataadapter" value="One Empty Record"/>
	<property name="ireport.zoom" value="1.771561000000001"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="570"/>
	<style name="MyCustomStyle" hAlign="Left" fontSize="10">
		<conditionalStyle>
			<conditionExpression><![CDATA[$P{IS_SEZ_NEW_AVVISI} && ( ($P{UDIENZA}.getDescTipo().equals("CC") && ($F{art127} || $F{art611}) ) || 

($P{UDIENZA}.getDescTipo().equals("CC") && $F{art127} && $F{art611_comma_1_quarter}) || 

($P{UDIENZA}.getDescTipo().equals("PU") && $F{art611_comma_1_quarter}) || 

( ($P{UDIENZA}.getDescTipo().equals("PU")) && (!$F{art127} && !$F{art444} && !$F{art438} && !$F{art611} && !$F{deplano} && !$F{art12} && !$F{art169}) && !$F{mae}) || ($F{tipo}.equals( "AE" )))]]></conditionExpression>
			<style hAlign="Center" fontSize="10"/>
		</conditionalStyle>
	</style>
	<parameter name="UDIENZA" class="sic.common.dto.Udienza" isForPrompting="false"/>
	<parameter name="SUBREPORT1" class="java.lang.String" isForPrompting="false">
		<defaultValueExpression><![CDATA["PENALE_AVVISO_UDIENZA_SUB.jasper"]]></defaultValueExpression>
	</parameter>
	<parameter name="SUBREPORT2" class="java.lang.String" isForPrompting="false"/>
	<parameter name="INAMMISSIBILE_SETTIMA" class="java.lang.Boolean" isForPrompting="false"/>
	<parameter name="REQUISITORIA" class="java.lang.Boolean" isForPrompting="false"/>
	<parameter name="REQUISITORIA_INAMM" class="java.lang.Boolean" isForPrompting="false"/>
	<parameter name="LOGO" class="java.io.InputStream" isForPrompting="false"/>
	<parameter name="DETPARTI" class="java.lang.String" isForPrompting="false"/>
	<parameter name="DATA_INVIO" class="java.lang.String"/>
	<parameter name="isMenuStampa" class="java.lang.Boolean"/>
	<parameter name="SUBREPORT3" class="java.lang.String" isForPrompting="false">
		<defaultValueExpression><![CDATA["PENALE_AVVISO_UDIENZA_SUB3.jasper"]]></defaultValueExpression>
	</parameter>
	<parameter name="IS_SEZ_NEW_AVVISI" class="java.lang.Boolean">
		<defaultValueExpression><![CDATA[!$P{UDIENZA}.getIdSezione().equals("S7")]]></defaultValueExpression>
	</parameter>
	<parameter name="TIPOCONTRAVVISO" class="java.lang.String" isForPrompting="false"/>
	<parameter name="PEC_DEPOSITO_ATTI" class="java.lang.String"/>
	<parameter name="PEC_UFFICIO" class="java.lang.String"/>
	<queryString language="SQL">
		<![CDATA[]]>
	</queryString>
	<field name="numeroRegistroGeneraleReale" class="java.lang.String">
		<fieldDescription><![CDATA[numeroRegistroGeneraleReale]]></fieldDescription>
	</field>
	<field name="conclusione1" class="java.lang.String">
		<fieldDescription><![CDATA[conclusione1]]></fieldDescription>
	</field>
	<field name="conclusione2" class="java.lang.String">
		<fieldDescription><![CDATA[conclusione2]]></fieldDescription>
	</field>
	<field name="conclusione3" class="java.lang.String">
		<fieldDescription><![CDATA[conclusione3]]></fieldDescription>
	</field>
	<field name="descInammiss1" class="java.lang.String">
		<fieldDescription><![CDATA[descInammiss1]]></fieldDescription>
	</field>
	<field name="descInammiss2" class="java.lang.String">
		<fieldDescription><![CDATA[descInammiss2]]></fieldDescription>
	</field>
	<field name="descInammiss3" class="java.lang.String">
		<fieldDescription><![CDATA[descInammiss3]]></fieldDescription>
	</field>
	<field name="art12" class="java.lang.Boolean">
		<fieldDescription><![CDATA[art12]]></fieldDescription>
	</field>
	<field name="art127" class="java.lang.Boolean">
		<fieldDescription><![CDATA[art127]]></fieldDescription>
	</field>
	<field name="art169" class="java.lang.Boolean">
		<fieldDescription><![CDATA[art169]]></fieldDescription>
	</field>
	<field name="art611" class="java.lang.Boolean">
		<fieldDescription><![CDATA[art611]]></fieldDescription>
	</field>
	<field name="provvedimentoImpugnato" class="sic.common.dto.ProvvedimentoImpugnato">
		<fieldDescription><![CDATA[provvedimentoImpugnato]]></fieldDescription>
	</field>
	<field name="urgente" class="java.lang.Boolean">
		<fieldDescription><![CDATA[urgente]]></fieldDescription>
	</field>
	<field name="tipo" class="java.lang.String">
		<fieldDescription><![CDATA[tipo]]></fieldDescription>
	</field>
	<field name="mandatoArrestoEuropeo" class="java.lang.String">
		<fieldDescription><![CDATA[mandatoArrestoEuropeo]]></fieldDescription>
	</field>
	<field name="notificaGiornata" class="java.lang.Boolean">
		<fieldDescription><![CDATA[notificaGiornata]]></fieldDescription>
	</field>
	<field name="notificaPresidente" class="java.lang.Boolean">
		<fieldDescription><![CDATA[notificaPresidente]]></fieldDescription>
	</field>
	<field name="numOrd" class="java.lang.Integer">
		<fieldDescription><![CDATA[numOrd]]></fieldDescription>
	</field>
	<field name="difensorePartiDifese" class="java.util.List">
		<fieldDescription><![CDATA[difensorePartiDifese]]></fieldDescription>
	</field>
	<field name="parti" class="java.lang.String">
		<fieldDescription><![CDATA[parti]]></fieldDescription>
	</field>
	<field name="dataDecretoFeriale" class="java.lang.String"/>
	<field name="listInammissibilita" class="java.util.List">
		<fieldDescription><![CDATA[listInammissibilita]]></fieldDescription>
	</field>
	<field name="art611_comma_1_quarter" class="java.lang.Boolean">
		<fieldDescription><![CDATA[art611_comma_1_quarter]]></fieldDescription>
	</field>
	<field name="art438" class="java.lang.Boolean">
		<fieldDescription><![CDATA[art438]]></fieldDescription>
	</field>
	<field name="art444" class="java.lang.Boolean">
		<fieldDescription><![CDATA[art444]]></fieldDescription>
	</field>
	<field name="deplano" class="java.lang.Boolean">
		<fieldDescription><![CDATA[deplano]]></fieldDescription>
	</field>
	<field name="mae" class="java.lang.Boolean">
		<fieldDescription><![CDATA[mae]]></fieldDescription>
	</field>
	<group name="Group1">
		<groupExpression><![CDATA[$F{numeroRegistroGeneraleReale}]]></groupExpression>
		<groupFooter>
			<band height="75" splitType="Prevent">
				<textField isStretchWithOverflow="true">
					<reportElement positionType="FixRelativeToBottom" x="0" y="50" width="180" height="15" uuid="d3410edc-c1ca-46e3-9b91-32b1e0014a70">
						<property name="local_mesure_unitheight" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineColor="#000000"/>
					</box>
					<textElement>
						<font fontName="SansSerif" isBold="true" pdfFontName="Helvetica-Bold" isPdfEmbedded="true"/>
					</textElement>
					<textFieldExpression><![CDATA["ROMA, LI "+ ($P{DATA_INVIO}!=null? $P{DATA_INVIO}:"")]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true">
					<reportElement positionType="FixRelativeToBottom" x="387" y="50" width="169" height="15" uuid="f769ea80-599a-4102-89d7-10ffbe76ed63">
						<property name="local_mesure_unitheight" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineColor="#000000"/>
					</box>
					<textElement>
						<font fontName="SansSerif" isBold="true" pdfFontName="Helvetica-Bold" isPdfEmbedded="true"/>
					</textElement>
					<textFieldExpression><![CDATA["IL CANCELLIERE"]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="0" y="17" width="554" height="20" isRemoveLineWhenBlank="true" uuid="58b9ad87-d912-40ea-b43d-d93c3839536a">
						<printWhenExpression><![CDATA[$P{isMenuStampa}==true]]></printWhenExpression>
					</reportElement>
					<textElement verticalAlignment="Middle"/>
					<textFieldExpression><![CDATA[$P{IS_SEZ_NEW_AVVISI} && ( $P{UDIENZA}.getDescTipo().equals("CC") && ($F{art127} || $F{art611})  || 

($P{UDIENZA}.getDescTipo().equals("CC") && $F{art127} && $F{art611_comma_1_quarter}) || 

($P{UDIENZA}.getDescTipo().equals("PU") && $F{art611_comma_1_quarter}) ||

( ($P{UDIENZA}.getDescTipo().equals("PU")) && (!$F{art127} && !$F{art444} && !$F{art438} && !$F{art611} && !$F{deplano} && !$F{art12} && !$F{art169}) && !$F{mae} ) || $F{tipo}.equals( "AE" ) ) ? "" : "SI PREGA DI VOLER COMUNICARE TEMPESTIVAMENTE A MEZZO FAX EVENTUALE OMESSA NOTIFICA"]]></textFieldExpression>
				</textField>
			</band>
		</groupFooter>
	</group>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band height="185" splitType="Stretch">
			<property name="local_mesure_unitheight" value="pixel"/>
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
			<staticText>
				<reportElement x="76" y="70" width="405" height="38" uuid="81d6aa17-c1a1-41ed-ad0a-5023fdfee396"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="SansSerif" size="16" isBold="true" pdfFontName="Helvetica-Bold" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[CORTE SUPREMA DI CASSAZIONE]]></text>
			</staticText>
			<textField isStretchWithOverflow="true">
				<reportElement positionType="Float" x="0" y="108" width="555" height="52" uuid="54f0fa89-d977-40fa-802c-9c4d56017b24"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="SansSerif"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{UDIENZA}.getDescSezione()+"\nPEC UFFICIO: "+$P{PEC_UFFICIO}+"\nPEC DEDICATA DEPOSITO ATTI: "+$P{PEC_DEPOSITO_ATTI}+"\n\n"+($P{TIPOCONTRAVVISO} != null ?"CONTRAVVISO" : "AVVISO")+" D'UDIENZA PENALE"]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true">
				<reportElement positionType="Float" x="480" y="10" width="75" height="20" uuid="35d9b7ed-6e74-4ad1-8a4f-aa7d196c0342">
					<property name="local_mesure_unitwidth" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Dashed"/>
					<leftPen lineWidth="1.0" lineStyle="Dashed"/>
					<bottomPen lineWidth="1.0" lineStyle="Dashed"/>
					<rightPen lineWidth="1.0" lineStyle="Dashed"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="SansSerif" isBold="true" pdfFontName="Helvetica-Bold" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{IS_SEZ_NEW_AVVISI} && ( ($P{UDIENZA}.getDescTipo().equals("CC") && ($F{art127} || $F{art611}) )  ||

($P{UDIENZA}.getDescTipo().equals("CC") && $F{art127} && $F{art611_comma_1_quarter}) ||

($P{UDIENZA}.getDescTipo().equals("PU") && $F{art611_comma_1_quarter}) ||

( ($P{UDIENZA}.getDescTipo().equals("PU")) && (!$F{art127} && !$F{art444} && !$F{art438} && !$F{art611} && !$F{deplano} && !$F{art12} && !$F{art169}) && !$F{mae}) || ($F{tipo}.equals( "AE" )) ) ? $P{UDIENZA}.getIdSezione() : $P{UDIENZA}.getIdSezione()+($F{art611}?"\n610-611":"")]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true">
				<reportElement positionType="Float" x="0" y="10" width="75" height="20" uuid="cfe023ec-070e-4dce-b2e6-52b1a0aaf1bd">
					<property name="local_mesure_unitwidth" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Dashed" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Dashed" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Dashed" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Dashed" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="SansSerif" isBold="true" pdfFontName="Helvetica-Bold" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{IS_SEZ_NEW_AVVISI} && ( ($P{UDIENZA}.getDescTipo().equals("CC") && ($F{art127} || $F{art611}) )  ||

($P{UDIENZA}.getDescTipo().equals("CC") && $F{art127} && $F{art611_comma_1_quarter}) ||

($P{UDIENZA}.getDescTipo().equals("PU") && $F{art611_comma_1_quarter}) ||

( ($P{UDIENZA}.getDescTipo().equals("PU")) && (!$F{art127} && !$F{art444} && !$F{art438} && !$F{art611} && !$F{deplano} && !$F{art12} && !$F{art169}) && !$F{mae}) || ($F{tipo}.equals( "AE" )) ) ? $P{UDIENZA}.getIdSezione() : $P{UDIENZA}.getIdSezione()+($F{art611}?"\n610-611":"")]]></textFieldExpression>
			</textField>
			<image>
				<reportElement positionType="Float" x="246" y="0" width="63" height="70" uuid="2d09464f-0e03-43c2-b70d-8e9961c2cec7">
					<property name="local_mesure_unitwidth" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<imageExpression><![CDATA[$P{LOGO}]]></imageExpression>
			</image>
		</band>
	</title>
	<pageHeader>
		<band splitType="Stretch">
			<property name="local_mesure_unitheight" value="pixel"/>
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
		</band>
	</pageHeader>
	<columnHeader>
		<band splitType="Stretch">
			<property name="local_mesure_unitheight" value="pixel"/>
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
		</band>
	</columnHeader>
	<detail>
		<band height="282" splitType="Stretch">
			<property name="local_mesure_unitheight" value="pixel"/>
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
			<subreport>
				<reportElement positionType="Float" x="0" y="75" width="555" height="16" forecolor="#000000" backcolor="#FFFFFF" uuid="148218c1-453e-48fb-b17f-6692a08533e7">
					<property name="local_mesure_unity" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="local_mesure_unitheight" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<subreportParameter name="SUBREPORT2">
					<subreportParameterExpression><![CDATA[$P{SUBREPORT2}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="ART_127">
					<subreportParameterExpression><![CDATA[$F{art127}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="TIPO_UDIENZA">
					<subreportParameterExpression><![CDATA[$P{UDIENZA}.getDescTipo()]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="ART_611_COMMA_1_QUATER">
					<subreportParameterExpression><![CDATA[$F{art611_comma_1_quarter}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="ART_12">
					<subreportParameterExpression><![CDATA[$F{art12}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="ART_169">
					<subreportParameterExpression><![CDATA[$F{art169}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="ART_438">
					<subreportParameterExpression><![CDATA[$F{art438}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="ART_444">
					<subreportParameterExpression><![CDATA[$F{art444}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="ART_611">
					<subreportParameterExpression><![CDATA[$F{art611}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="DEPLANO">
					<subreportParameterExpression><![CDATA[$F{deplano}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="MAE">
					<subreportParameterExpression><![CDATA[$F{mae}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="TIPO">
					<subreportParameterExpression><![CDATA[$F{tipo}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="IS_SEZ_NEW_AVVISI">
					<subreportParameterExpression><![CDATA[$P{IS_SEZ_NEW_AVVISI}]]></subreportParameterExpression>
				</subreportParameter>
				<dataSourceExpression><![CDATA[new net.sf.jasperreports.engine.data.JRBeanCollectionDataSource($F{difensorePartiDifese})]]></dataSourceExpression>
				<subreportExpression><![CDATA[$P{SUBREPORT1}]]></subreportExpression>
			</subreport>
			<textField isStretchWithOverflow="true">
				<reportElement style="MyCustomStyle" positionType="Float" x="0" y="45" width="554" height="15" uuid="a06def88-ef99-495c-9eb3-99863dcf5571">
					<property name="local_mesure_unitheight" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<textElement>
					<font fontName="SansSerif"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{IS_SEZ_NEW_AVVISI} && ( ($P{UDIENZA}.getDescTipo().equals("CC") && ($F{art127} || $F{art611}) ) || 

($P{UDIENZA}.getDescTipo().equals("CC") && $F{art127} && $F{art611_comma_1_quarter}) || 

($P{UDIENZA}.getDescTipo().equals("PU") && $F{art611_comma_1_quarter}) || 

( ($P{UDIENZA}.getDescTipo().equals("PU")) && (!$F{art127} && !$F{art444} && !$F{art438} && !$F{art611} && !$F{deplano} && !$F{art12} && !$F{art169}) && !$F{mae}) || ($F{tipo}.equals( "AE" ))) ? "SI DA' AVVISO\n" : ($F{art611}? "A NORMA DEGLI ART. 610 E 611 C.P.P. " : "")+"SI DA' AVVISO\n"]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement positionType="Float" x="0" y="60" width="554" height="15" uuid="6a316548-0168-4f28-98a0-3c52060c3bb2">
					<property name="local_mesure_unitheight" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<box>
					<bottomPen lineWidth="0.0"/>
				</box>
				<textElement>
					<font fontName="SansSerif"/>
				</textElement>
				<text><![CDATA[- AL PROCURATORE GENERALE PRESSO LA CORTE DI CASSAZIONE]]></text>
			</staticText>
			<textField isStretchWithOverflow="true">
				<reportElement positionType="Float" x="0" y="91" width="555" height="15" uuid="e1fdfc9d-3827-4b3c-bde6-91aedfaa0556">
					<property name="local_mesure_unitheight" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Dashed" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineColor="#000000"/>
				</box>
				<textElement markup="none">
					<font fontName="SansSerif"/>
					<paragraph lineSpacing="1_1_2" lineSpacingSize="1.0"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{tipo}.equals("RR")
?"\nCHE LA DECISIONE DELL'ISTANZA DI RIMESSIONE PROPOSTA DA :\n"+$P{DETPARTI}+"\n"
:"\nCHE LA DECISIONE DEL PROCEDIMENTO " +($P{TIPOCONTRAVVISO}!=null?$P{TIPOCONTRAVVISO}:"")+ " PROPOSTO DA :\n"+$P{DETPARTI}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true">
				<reportElement positionType="Float" x="0" y="0" width="555" height="15" uuid="bbd9be5d-a471-4cee-aa54-920c50aca163">
					<property name="local_mesure_unitheight" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center">
					<font fontName="SansSerif" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{urgente}?"URGENTE":""]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true">
				<reportElement positionType="Float" x="0" y="114" width="555" height="15" uuid="87bf4ca2-63d8-45bb-a474-2009d0e5c00c">
					<property name="local_mesure_unitheight" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineColor="#000000"/>
				</box>
				<textElement>
					<font fontName="SansSerif"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{tipo}.equals("RR")
?"TRASMESSA IN DATA "+$F{provvedimentoImpugnato}.getData()+" DAL "+$F{provvedimentoImpugnato}.getAutoritaLocale()+" DI "+$F{provvedimentoImpugnato}.getLocalita()
:"AVVERSO "+$F{provvedimentoImpugnato}.getNatura()+" IN DATA "+$F{provvedimentoImpugnato}.getData()+" DEL "+$F{provvedimentoImpugnato}.getAutoritaLocale()+" DI "+$F{provvedimentoImpugnato}.getLocalita()]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true">
				<reportElement positionType="Float" x="0" y="138" width="555" height="15" uuid="506f12d1-387d-4b15-befb-145ac5d32d50">
					<property name="local_mesure_unitheight" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineColor="#000000"/>
				</box>
				<textElement markup="styled">
					<font fontName="SansSerif"/>
				</textElement>
				<textFieldExpression><![CDATA[( $F{tipo}.equals( "AE" ) && $P{IS_SEZ_NEW_AVVISI} ) ? "NELLA CAMERA DI CONSIGLIO DEL <style isBold='true' pdfFontName='Helvetica-Bold'>"+$P{UDIENZA}.getDataUd()+"</style> CON UDIENZA CAMERALE PARTECIPATA AI SENSI DELL’ART. 22, COMMA 3, L. N. 69/2005" :

($P{UDIENZA}.getDescTipo().equals("CC") && $F{art611} && $P{IS_SEZ_NEW_AVVISI} ) ? "È FISSATA PER L’UDIENZA DEL <style isBold='true' pdfFontName='Helvetica-Bold'>"+$P{UDIENZA}.getDataUd()+"</style> IN CAMERA DI CONSIGLIO, SENZA LA PARTECIPAZIONE DEL PROCURATORE GENERALE E DEI DIFENSORI" : 

( $P{UDIENZA}.getDescTipo().equals("PU") && $F{art611_comma_1_quarter} && $P{IS_SEZ_NEW_AVVISI}) ? "E' FISSATA PER L'UDIENZA DEL <style isBold='true' pdfFontName='Helvetica-Bold'>"+$P{UDIENZA}.getDataUd()+"</style> ALLE ORE 10 IN PUBBLICA UDIENZA, COME DISPOSTO DI UFFICIO DALLA CORTE AI SENSI DELL’ART. 611, COMMA 1 <style pdfFontName='Helvetica-Oblique'>QUATER</style>, COD. PROC. PEN." :

( ($P{UDIENZA}.getDescTipo().equals("PU")) && (!$F{art127} && !$F{art444} && !$F{art438} && !$F{art611} && !$F{deplano} && !$F{art12} && !$F{art169}) && !$F{mae} && $P{IS_SEZ_NEW_AVVISI}) ? "È FISSATA PER L’UDIENZA DEL <style isBold='true' pdfFontName='Helvetica-Bold'>"+$P{UDIENZA}.getDataUd()+"</style> IN CAMERA DI CONSIGLIO, SENZA LA PRESENZA DELLE PARTI, SALVO CHE LE STESSE RICHIEDANO LA TRATTAZIONE IN PUBBLICA UDIENZA ENTRO IL TERMINE PERENTORIO DI VENTICINQUE GIORNI LIBERI PRIMA DELL’UDIENZA" :

( $P{UDIENZA}.getDescTipo().equals("CC") && $F{art127} && ($P{IS_SEZ_NEW_AVVISI})  ) ? "E' FISSATA, PER L'UDIENZA DEL <style isBold='true' pdfFontName='Helvetica-Bold'>"+$P{UDIENZA}.getDataUd()+"</style> ALLE ORE 10 IN CAMERA DI CONSIGLIO" +($F{art611_comma_1_quarter} ? ", COME DISPOSTO DI UFFICIO DALLA CORTE AI SENSI DELL’ART. 611, COMMA 1 <style pdfFontName='Helvetica-Oblique'>QUATER</style>, COD. PROC. PEN." :", SENZA LA PRESENZA DELLE PARTI*.") :


( "E' FISSATA PER L'UDIENZA DI "+($P{UDIENZA}.getDescTipo().equals("CC")?"CAMERA DI CONSIGLIO":"PUBBLICA UDIENZA") + ($F{art611}? ", SENZA INTERVENTO DEI DIFENSORI, " : " ") +" COLLEGIO "+$P{UDIENZA}.getAula()+" DEL GIORNO <style isBold='true' pdfFontName='Helvetica-Bold'>"+$P{UDIENZA}.getDataUd()+"</style> ALLE ORE 10.00."
+($F{art127}?" SI OSSERVANO LE FORME PREVISTE DALL'ART.127 C.P.P.":""))]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true">
				<reportElement positionType="Float" x="0" y="214" width="555" height="15" isRemoveLineWhenBlank="true" uuid="f63f7a0b-4b6f-4f92-87c1-57f8cf0e4f8b">
					<property name="local_mesure_unitheight" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineColor="#000000"/>
				</box>
				<textElement>
					<font fontName="SansSerif" isBold="true" pdfFontName="Helvetica-Bold" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA[($P{UDIENZA}.getIdSezione().equals("SF")?"DICHIARATA L'URGENZA DEL PROCEDIMENTO AI SENSI DELL'ART. 240 BIS, D.L.VO 28-7-1989 N°271,CON DECRETO "+$F{dataDecretoFeriale}+" ALLEGATO AGLI ATTI, PER L'IMMINENTE SCADENZA DEL TERMINE DI PRESCRIZIONE/DELLA CUSTODIA CAUTELARE":"")]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true">
				<reportElement positionType="Float" x="0" y="252" width="555" height="15" isRemoveLineWhenBlank="true" uuid="846af8a9-fe98-4b38-87f7-e501709a077e">
					<property name="local_mesure_unitheight" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineColor="#000000"/>
				</box>
				<textElement>
					<font fontName="SansSerif"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{IS_SEZ_NEW_AVVISI} && ( $P{UDIENZA}.getDescTipo().equals("CC") && ($F{art127} || $F{art611})  || 

($P{UDIENZA}.getDescTipo().equals("CC") && $F{art127} && $F{art611_comma_1_quarter}) || 

($P{UDIENZA}.getDescTipo().equals("PU") && $F{art611_comma_1_quarter}) ||

( ($P{UDIENZA}.getDescTipo().equals("PU")) && (!$F{art127} && !$F{art444} && !$F{art438} && !$F{art611} && !$F{deplano} && !$F{art12} && !$F{art169}) && !$F{mae} ) ) ? "" : "\n\nANNOTAZIONI:"]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true">
				<reportElement positionType="Float" x="0" y="267" width="555" height="15" isRemoveLineWhenBlank="true" uuid="8c53a9c9-e567-40c6-aa17-418ef773e29d">
					<property name="local_mesure_unitheight" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineColor="#000000"/>
				</box>
				<textElement>
					<font fontName="SansSerif" isBold="true" pdfFontName="Helvetica-Bold" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{tipo}.equals( "AE" )? "" : (($F{mandatoArrestoEuropeo}!=null && !$F{mandatoArrestoEuropeo}.equals("")?"\t"+$F{mandatoArrestoEuropeo}+"\n":"")+ ($F{notificaPresidente}?"\tNOTIFICARE PERSONALMENTE D'ORDINE PRES.\n":"")+ ($F{notificaGiornata}?"\tNOTIFICARE IN GIORNATA\n":"")+ ($F{tipo}.equals("EC")?"\tCautelare in Estradizione\n":"")+ ($F{art169}?"\tART.169 N.A. C.P.P.":""))]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true">
				<reportElement positionType="Float" x="440" y="15" width="114" height="15" uuid="d0d9cafe-aeb5-4c92-8021-391e6c571671">
					<property name="local_mesure_unitheight" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<textElement textAlignment="Left">
					<font fontName="SansSerif" isBold="true" pdfFontName="Helvetica-Bold" pdfEncoding="" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{IS_SEZ_NEW_AVVISI} && ( ($P{UDIENZA}.getDescTipo().equals("CC") && ($F{art127} || $F{art611}) )  ||

($P{UDIENZA}.getDescTipo().equals("CC") && $F{art127} && $F{art611_comma_1_quarter}) ||

($P{UDIENZA}.getDescTipo().equals("PU") && $F{art611_comma_1_quarter}) ||

( ($P{UDIENZA}.getDescTipo().equals("PU")) && (!$F{art127} && !$F{art444} && !$F{art438} && !$F{art611} && !$F{deplano} && !$F{art12} && !$F{art169}) && !$F{mae}) || ($F{tipo}.equals( "AE" )) ) ? "" : "NUM ORD: "+$F{numOrd}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true">
				<reportElement positionType="Float" x="80" y="15" width="360" height="15" uuid="65756ac9-237b-45ef-8a55-4ae62c959498">
					<property name="local_mesure_unitheight" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<textElement textAlignment="Center" markup="styled">
					<font fontName="SansSerif"/>
				</textElement>
				<textFieldExpression><![CDATA[( $P{IS_SEZ_NEW_AVVISI} && (($P{UDIENZA}.getDescTipo().equals("CC") && ($F{art127} || $F{art611} ) ) || 
	($P{UDIENZA}.getDescTipo().equals("CC") && $F{art127} && $F{art611_comma_1_quarter}) || 
	($P{UDIENZA}.getDescTipo().equals("PU") && $F{art611_comma_1_quarter}) || 

( ($P{UDIENZA}.getDescTipo().equals("PU")) && (!$F{art127} && !$F{art444} && !$F{art438} && !$F{art611} && !$F{deplano} && !$F{art12} && !$F{art169}) && !$F{mae}) || 
($F{tipo}.equals( "AE" )) ) ? "\t" : ""
) +"NUMERO <style isBold='true'  pdfFontName='Helvetica-Bold'>"+$F{numeroRegistroGeneraleReale}+"</style> DEL REGISTRO GENERALE"]]></textFieldExpression>
			</textField>
			<subreport>
				<reportElement positionType="Float" x="0" y="198" width="555" height="16" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#FFFFFF" uuid="d28a1870-1277-4c38-8653-fe92f968774a">
					<property name="local_mesure_unity" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="local_mesure_unitheight" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<printWhenExpression><![CDATA[new Boolean(($P{UDIENZA}.getIdSezione().equals("S7")&&($P{INAMMISSIBILE_SETTIMA})))]]></printWhenExpression>
				</reportElement>
				<subreportParameter name="SUBREPORT2">
					<subreportParameterExpression><![CDATA[$P{SUBREPORT2}]]></subreportParameterExpression>
				</subreportParameter>
				<dataSourceExpression><![CDATA[new net.sf.jasperreports.engine.data.JRBeanCollectionDataSource($F{listInammissibilita})]]></dataSourceExpression>
				<subreportExpression><![CDATA[$P{SUBREPORT3}]]></subreportExpression>
			</subreport>
			<textField isStretchWithOverflow="true">
				<reportElement positionType="Float" x="-1" y="185" width="555" height="15" uuid="b0c06136-4e49-4597-8106-db5109474d02">
					<property name="local_mesure_unitheight" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<printWhenExpression><![CDATA[new Boolean(($P{UDIENZA}.getIdSezione().equals("S7") && $P{INAMMISSIBILE_SETTIMA}))]]></printWhenExpression>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineColor="#000000"/>
				</box>
				<textElement markup="styled">
					<font fontName="SansSerif" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression><![CDATA["RILEVATA INAMMISSIBILITA' PERCHE':"]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true">
				<reportElement positionType="Float" x="2" y="160" width="554" height="15" uuid="cfefe7fe-da88-4b7c-933c-e367643beb03">
					<printWhenExpression><![CDATA[$F{art169}==true]]></printWhenExpression>
				</reportElement>
				<textFieldExpression><![CDATA["AI SENSI DELL'ART.169 DISP. ATT. C.P.P. I TERMINI RELATIVI ALLE NOTIFICHE DEGLI AVVISI DI UDIENZA SONO RIDOTTI DI 1/3."]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true">
				<reportElement positionType="Float" x="0" y="233" width="418" height="15" isRemoveLineWhenBlank="true" uuid="fc14449e-a934-4d23-87a3-c06437de3f89"/>
				<textFieldExpression><![CDATA[$P{IS_SEZ_NEW_AVVISI} && ( $P{UDIENZA}.getDescTipo().equals("CC") && ($F{art127} || $F{art611}) || 

($P{UDIENZA}.getDescTipo().equals("CC") && $F{art127} && $F{art611_comma_1_quarter}) || 

($P{UDIENZA}.getDescTipo().equals("PU") && $F{art611_comma_1_quarter}) ||

( ($P{UDIENZA}.getDescTipo().equals("PU")) && (!$F{art127} && !$F{art444} && !$F{art438} && !$F{art611} && !$F{deplano} && !$F{art12} && !$F{art169}) && !$F{mae}) ) ? "" : "ATTI PROCESSUALI DEPOSITATI IN CANCELLERIA"]]></textFieldExpression>
			</textField>
		</band>
	</detail>
	<columnFooter>
		<band splitType="Stretch">
			<property name="local_mesure_unitheight" value="pixel"/>
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
		</band>
	</columnFooter>
	<lastPageFooter>
		<band height="110" splitType="Stretch">
			<property name="local_mesure_unitheight" value="pixel"/>
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
			<textField isStretchWithOverflow="true">
				<reportElement positionType="FixRelativeToBottom" x="0" y="0" width="555" height="74" isRemoveLineWhenBlank="true" uuid="ad0b6881-4f40-495a-ae7c-43ed7b6d2b12">
					<property name="local_mesure_unitwidth" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="local_mesure_unitheight" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<textFieldExpression><![CDATA[(($P{UDIENZA}.getDescTipo().equals("CC") && $F{art127} && !$F{art611_comma_1_quarter} && $P{IS_SEZ_NEW_AVVISI} && !$P{UDIENZA}.getIdSezione().equals("SF") && !$P{UDIENZA}.getIdSezione().equals("SU") && !$F{tipo}.equals( "AE" )) ? "*Legenda\n1.Per i ricorsi proposti entro il 30 giugno 2024 il Procuratore generale e il difensore abilitato a norma dell’art. 613 cod. proc. pen. possono chiedere la trattazione in camera di consiglio con le forme previste dall’art. 127 cod. proc. pen entro il termine perentorio di venticinque giorni liberi prima dell’udienza\n\n2.Per i ricorsi proposti dal 1° luglio 2024 il Procuratore generale e il difensore abilitato a norma dell’art. 613 cod. proc. pen. possono chiedere la trattazione in camera di consiglio con le forme previste dall’art. 127 cod. proc. pen entro il termine perentorio di quindici giorni liberi prima dell’udienza." :
	
($P{UDIENZA}.getDescTipo().equals("CC") && $F{art127} && !$F{art611_comma_1_quarter} && ($P{UDIENZA}.getIdSezione().equals("SF") || $P{UDIENZA}.getIdSezione().equals("SU")) && !$F{tipo}.equals( "AE" )) ? "*Legenda\nPer i ricorsi proposti dal 1° luglio del 2024 il Procuratore generale e il difensore abilitato a norma dell'art. 613 cod. proc. pen. possono chiedere la trattazione in camera di consiglio con le forme previste dall'art. 127 cod. proc. pen. entro il termine perentorio di quindici giorni liberi prima dell'udienza." : "") 
]]></textFieldExpression>
			</textField>
		</band>
	</lastPageFooter>
	<summary>
		<band splitType="Stretch">
			<property name="local_mesure_unitheight" value="pixel"/>
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
		</band>
	</summary>
</jasperReport>
